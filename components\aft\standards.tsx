"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

export default function AFTStandardsComponent() {
  const testEvents = [
    {
      name: "Push-ups",
      description: "2-minute maximum repetition test",
      procedure: [
        "Start in the front leaning rest position",
        "Lower body until chest touches the ground",
        "Push up to starting position",
        "Repeat for maximum repetitions in 2 minutes"
      ],
      scoring: "Based on age and gender-specific standards"
    },
    {
      name: "Sit-ups",
      description: "2-minute maximum repetition test",
      procedure: [
        "Start in supine position with knees bent",
        "Hands interlocked behind head",
        "Sit up until elbows touch knees",
        "Return to starting position",
        "Repeat for maximum repetitions in 2 minutes"
      ],
      scoring: "Based on age and gender-specific standards"
    },
    {
      name: "2-Mile Run",
      description: "Timed 2-mile run for cardiovascular endurance",
      procedure: [
        "Run 2 miles on a measured course",
        "Complete the distance in minimum time",
        "Walking is permitted but not recommended",
        "Time is recorded upon crossing finish line"
      ],
      scoring: "Based on completion time and age/gender standards"
    }
  ];

  const ageGroups = [
    { range: "17-21", male: { pushups: "42+", situps: "53+", run: "15:54" }, female: { pushups: "19+", situps: "53+", run: "18:54" } },
    { range: "22-26", male: { pushups: "40+", situps: "50+", run: "16:36" }, female: { pushups: "17+", situps: "50+", run: "19:36" } },
    { range: "27-31", male: { pushups: "39+", situps: "45+", run: "17:00" }, female: { pushups: "17+", situps: "45+", run: "20:30" } },
    { range: "32-36", male: { pushups: "36+", situps: "42+", run: "17:42" }, female: { pushups: "15+", situps: "42+", run: "21:42" } },
    { range: "37-41", male: { pushups: "34+", situps: "38+", run: "18:18" }, female: { pushups: "13+", situps: "38+", run: "22:42" } },
    { range: "42-46", male: { pushups: "30+", situps: "32+", run: "19:30" }, female: { pushups: "12+", situps: "32+", run: "24:00" } },
  ];

  const gradingScale = [
    { range: "90-100", grade: "Excellent", color: "bg-green-100 text-green-800" },
    { range: "80-89", grade: "Good", color: "bg-blue-100 text-blue-800" },
    { range: "70-79", grade: "Satisfactory", color: "bg-yellow-100 text-yellow-800" },
    { range: "60-69", grade: "Needs Improvement", color: "bg-orange-100 text-orange-800" },
    { range: "0-59", grade: "Unsatisfactory", color: "bg-red-100 text-red-800" },
  ];

  return (
    <div className="space-y-6">
      <Tabs defaultValue="events" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="events">Test Events</TabsTrigger>
          <TabsTrigger value="standards">Minimum Standards</TabsTrigger>
          <TabsTrigger value="grading">Grading Scale</TabsTrigger>
          <TabsTrigger value="regulations">Regulations</TabsTrigger>
        </TabsList>
        
        <TabsContent value="events" className="space-y-4">
          <div className="grid gap-6">
            {testEvents.map((event, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {event.name}
                    <Badge variant="secondary">{event.description}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Test Procedure:</h4>
                      <ol className="list-decimal list-inside space-y-1 text-gray-600">
                        {event.procedure.map((step, stepIndex) => (
                          <li key={stepIndex}>{step}</li>
                        ))}
                      </ol>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Scoring:</h4>
                      <p className="text-gray-600">{event.scoring}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="standards" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Minimum Passing Standards by Age Group</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">Age Group</th>
                      <th className="text-left p-3">Gender</th>
                      <th className="text-left p-3">Push-ups</th>
                      <th className="text-left p-3">Sit-ups</th>
                      <th className="text-left p-3">2-Mile Run</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ageGroups.map((group, index) => (
                      <>
                        <tr key={`${index}-male`} className="border-b hover:bg-gray-50">
                          <td className="p-3 font-medium">{group.range}</td>
                          <td className="p-3">Male</td>
                          <td className="p-3">{group.male.pushups}</td>
                          <td className="p-3">{group.male.situps}</td>
                          <td className="p-3">{group.male.run}</td>
                        </tr>
                        <tr key={`${index}-female`} className="border-b hover:bg-gray-50">
                          <td className="p-3 font-medium">{group.range}</td>
                          <td className="p-3">Female</td>
                          <td className="p-3">{group.female.pushups}</td>
                          <td className="p-3">{group.female.situps}</td>
                          <td className="p-3">{group.female.run}</td>
                        </tr>
                      </>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="grading" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AFT Grading Scale</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {gradingScale.map((grade, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge className={grade.color}>{grade.grade}</Badge>
                      <span className="font-medium">{grade.range} points</span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {grade.grade === "Excellent" && "Outstanding performance"}
                      {grade.grade === "Good" && "Above average performance"}
                      {grade.grade === "Satisfactory" && "Meets standards"}
                      {grade.grade === "Needs Improvement" && "Below standards"}
                      {grade.grade === "Unsatisfactory" && "Fails to meet minimum standards"}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="regulations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AFT Regulations and Policies</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Test Administration</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-600">
                  <li>AFT must be administered by certified personnel</li>
                  <li>Proper warm-up period is required before testing</li>
                  <li>Environmental conditions must be suitable for testing</li>
                  <li>Equipment must meet Army standards</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Test Frequency</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-600">
                  <li>Conducted semi-annually for most personnel</li>
                  <li>Additional tests may be required based on unit policy</li>
                  <li>Make-up tests available for valid absences</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Special Considerations</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-600">
                  <li>Medical profiles may modify test requirements</li>
                  <li>Pregnancy and postpartum policies apply</li>
                  <li>Age-related modifications for senior personnel</li>
                  <li>Alternative events for certain medical conditions</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
