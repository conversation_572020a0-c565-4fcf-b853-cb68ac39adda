"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface AFTScore {
  pushUps: number;
  sitUps: number;
  twoMileRun: number;
  total: number;
  grade: string;
}

export default function AFTCalculatorComponent() {
  const [age, setAge] = useState<string>("");
  const [gender, setGender] = useState<string>("");
  const [pushUps, setPushUps] = useState<string>("");
  const [sitUps, setSitUps] = useState<string>("");
  const [runMinutes, setRunMinutes] = useState<string>("");
  const [runSeconds, setRunSeconds] = useState<string>("");
  const [score, setScore] = useState<AFTScore | null>(null);

  const calculateScore = () => {
    if (!age || !gender || !pushUps || !sitUps || !runMinutes || !runSeconds) {
      alert("Please fill in all fields");
      return;
    }

    // Simplified scoring logic - in a real app, this would use official Army tables
    const ageNum = parseInt(age);
    const pushUpsNum = parseInt(pushUps);
    const sitUpsNum = parseInt(sitUps);
    const totalRunSeconds = parseInt(runMinutes) * 60 + parseInt(runSeconds);

    // Basic scoring calculation (simplified)
    let pushUpScore = Math.min(100, Math.max(0, pushUpsNum * 2));
    let sitUpScore = Math.min(100, Math.max(0, sitUpsNum * 2));
    let runScore = Math.min(100, Math.max(0, 100 - (totalRunSeconds - 720) / 10));

    const totalScore = pushUpScore + sitUpScore + runScore;
    const averageScore = totalScore / 3;

    let grade = "Fail";
    if (averageScore >= 90) grade = "Excellent";
    else if (averageScore >= 80) grade = "Good";
    else if (averageScore >= 70) grade = "Satisfactory";
    else if (averageScore >= 60) grade = "Needs Improvement";

    setScore({
      pushUps: Math.round(pushUpScore),
      sitUps: Math.round(sitUpScore),
      twoMileRun: Math.round(runScore),
      total: Math.round(totalScore),
      grade
    });
  };

  const resetForm = () => {
    setAge("");
    setGender("");
    setPushUps("");
    setSitUps("");
    setRunMinutes("");
    setRunSeconds("");
    setScore(null);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Enter Your AFT Test Results</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="age">Age</Label>
              <Input
                id="age"
                type="number"
                placeholder="Enter your age"
                value={age}
                onChange={(e) => setAge(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="gender">Gender</Label>
              <Select value={gender} onValueChange={setGender}>
                <SelectTrigger>
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="pushups">Push-ups (2 minutes)</Label>
              <Input
                id="pushups"
                type="number"
                placeholder="Number of push-ups"
                value={pushUps}
                onChange={(e) => setPushUps(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="situps">Sit-ups (2 minutes)</Label>
              <Input
                id="situps"
                type="number"
                placeholder="Number of sit-ups"
                value={sitUps}
                onChange={(e) => setSitUps(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>2-Mile Run Time</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="number"
                placeholder="Minutes"
                value={runMinutes}
                onChange={(e) => setRunMinutes(e.target.value)}
              />
              <Input
                type="number"
                placeholder="Seconds"
                value={runSeconds}
                onChange={(e) => setRunSeconds(e.target.value)}
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={calculateScore} className="flex-1">
              Calculate AFT Score
            </Button>
            <Button onClick={resetForm} variant="outline">
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {score && (
        <Card>
          <CardHeader>
            <CardTitle>Your AFT Score Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{score.pushUps}</div>
                <div className="text-sm text-gray-600">Push-ups</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{score.sitUps}</div>
                <div className="text-sm text-gray-600">Sit-ups</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{score.twoMileRun}</div>
                <div className="text-sm text-gray-600">2-Mile Run</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">{score.total}</div>
                <div className="text-sm text-gray-600">Total Score</div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-xl font-semibold mb-2">Grade: {score.grade}</div>
              <div className="text-gray-600">
                Average Score: {Math.round(score.total / 3)} points
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
