"use client";

import { useEffect, useRef, useState } from "react";

interface ResponsiveIframeProps {
  src: string;
  title: string;
  minHeight?: string;
  className?: string;
  maxHeight?: string;
}

export default function ResponsiveIframe({
  src,
  title,
  minHeight = "650px",
  maxHeight = "1200px",
  className = ""
}: ResponsiveIframeProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeHeight, setIframeHeight] = useState(minHeight);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 只接受来自目标域的消息
      if (event.origin !== "https://www.aftcalculator.online") {
        return;
      }

      // 如果收到高度信息，更新iframe高度
      if (event.data && event.data.type === "resize" && event.data.height) {
        const newHeight = Math.min(
          Math.max(parseInt(event.data.height), 400), // 最小高度400px
          parseInt(maxHeight) // 最大高度限制
        );
        setIframeHeight(`${newHeight}px`);
      }
    };

    // 定期检查iframe高度的函数
    const checkIframeHeight = () => {
      if (iframeRef.current && isLoaded) {
        try {
          const iframeDoc = iframeRef.current.contentDocument;
          if (iframeDoc) {
            const height = Math.max(
              iframeDoc.body.scrollHeight,
              iframeDoc.body.offsetHeight,
              iframeDoc.documentElement.clientHeight,
              iframeDoc.documentElement.scrollHeight,
              iframeDoc.documentElement.offsetHeight
            );
            if (height > 0 && height !== parseInt(iframeHeight)) {
              const adjustedHeight = Math.min(
                Math.max(height + 20, 400), // 添加20px缓冲和最小高度
                parseInt(maxHeight) // 最大高度限制
              );
              setIframeHeight(`${adjustedHeight}px`);
            }
          }
        } catch (error) {
          // 跨域限制，忽略错误
        }
      }
    };

    window.addEventListener("message", handleMessage);

    // 设置定期检查
    const heightCheckInterval = setInterval(checkIframeHeight, 2000);

    return () => {
      window.removeEventListener("message", handleMessage);
      clearInterval(heightCheckInterval);
    };
  }, [isLoaded, iframeHeight]);

  return (
    <div className={`w-full bg-white relative ${className}`}>
      {!isLoaded && (
        <div
          className="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg"
          style={{ minHeight }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">Loading AFT Calculator...</p>
          </div>
        </div>
      )}
      <iframe
        ref={iframeRef}
        src={src}
        title={title}
        className="w-full transition-opacity duration-300"
        style={{
          height: iframeHeight,
          border: 'none',
          display: 'block',
          backgroundColor: 'white',
          opacity: isLoaded ? 1 : 0
        }}
        allowFullScreen
        loading="lazy"
        onLoad={() => {
          setIsLoaded(true);

          // 延迟执行高度检测，让iframe内容完全加载
          setTimeout(() => {
            if (iframeRef.current) {
              try {
                const iframeDoc = iframeRef.current.contentDocument;
                if (iframeDoc) {
                  // 等待内容渲染完成
                  setTimeout(() => {
                    const height = Math.max(
                      iframeDoc.body.scrollHeight,
                      iframeDoc.body.offsetHeight,
                      iframeDoc.documentElement.clientHeight,
                      iframeDoc.documentElement.scrollHeight,
                      iframeDoc.documentElement.offsetHeight
                    );
                    if (height > 0) {
                      const adjustedHeight = Math.min(
                        Math.max(height + 20, 400), // 添加缓冲和最小高度
                        parseInt(maxHeight) // 最大高度限制
                      );
                      setIframeHeight(`${adjustedHeight}px`);
                    }
                  }, 500);
                }
              } catch (error) {
                // 跨域限制，无法访问iframe内容
                console.log("Cannot access iframe content due to CORS policy");
                // 根据URL设置不同的默认高度
                const defaultHeight = src.includes('embed-aft-calculator') ? '800px' : '600px';
                setIframeHeight(defaultHeight);
              }
            }
          }, 100);
        }}
      />
    </div>
  );
}
