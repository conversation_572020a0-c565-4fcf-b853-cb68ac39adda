import GoogleAnalytics from "./google-analytics";
import GoogleAdSense from "./google-adsense";
import MicrosoftClarity from "./microsoft-clarity";
import OpenPanelAnalytics from "./open-panel";
import Plausible from "./plausible";

export default function Analytics() {
  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  return (
    <>
      <OpenPanelAnalytics />
      <GoogleAnalytics />
      <GoogleAdSense />
      <MicrosoftClarity />
      <Plausible />
    </>
  );
}
